# 🏗️ Authentication & User Management System - Implementation Documentation

## 📋 System Overview

This document details the complete implementation of the authentication and user management system for the property management application. The system is designed to work completely offline using mock data without requiring any external database or Redis connections.

## 🏛️ Architecture

### System Components

```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend Applications                     │
├─────────────────────────────────┬───────────────────────────┤
│         Main App (Customer)     │      Admin App            │
│         Port: 3000              │      Port: 3001           │
│                                 │                           │
│  ┌─────────────────────────┐   │  ┌─────────────────────┐  │
│  │     Authentication      │   │  │   Admin Auth        │  │
│  │     - Email/Password    │   │  │   - Email/Password  │  │
│  │     - Phone/Password    │   │  │   - Role-based      │  │
│  │     - OTP Login         │   │  │   - Permissions     │  │
│  └─────────────────────────┘   │  └─────────────────────┘  │
│                                 │                           │
│  ┌─────────────────────────┐   │  ┌─────────────────────┐  │
│  │     User Dashboard      │   │  │   Admin Dashboard   │  │
│  │     - Transactions      │   │  │   - System Overview │  │
│  │     - Payments          │   │  │   - User Management │  │
│  │     - Profile           │   │  │   - Settings        │  │
│  └─────────────────────────┘   │  └─────────────────────┘  │
└─────────────────────────────────┴───────────────────────────┘
                                │
                                ▼
                    ┌─────────────────────────┐
                    │      Mock Data Layer    │
                    │                         │
                    │  ┌─────────────────┐   │
                    │  │  Customer Data  │   │
                    │  │  - 2 Users      │   │
                    │  │  - 120+ Trans   │   │
                    │  │  - 24 Payments  │   │
                    │  └─────────────────┘   │
                    │                         │
                    │  ┌─────────────────┐   │
                    │  │   Admin Data    │   │
                    │  │  - 2 <PERSON><PERSON>     │   │
                    │  │  - Permissions  │   │
                    │  │  - Access Levels│   │
                    │  └─────────────────┘   │
                    └─────────────────────────┘
```

## 📁 File Structure

### Main Application
```
main/src/
├── app/
│   ├── (auth)/
│   │   ├── login/page.tsx              # Enhanced login with OTP option
│   │   └── otp-login/page.tsx          # OTP verification page
│   ├── (user)/
│   │   ├── dashboard/page.tsx          # User dashboard with mock data
│   │   ├── profile/page.tsx            # User profile management
│   │   └── settings/page.tsx           # User settings with sign out
│   └── api/auth/
│       ├── login/route.ts              # Enhanced login API
│       ├── send-otp/route.ts           # OTP sending API
│       ├── resend-otp/route.ts         # OTP resending API
│       └── verify-otp/route.ts         # OTP verification API
├── context/
│   └── AuthContext.tsx                 # Authentication context
├── data/
│   ├── mockUsers.ts                    # Mock user data
│   └── mockOTP.ts                      # Mock OTP service
└── lib/
    ├── auth-utils.ts                   # Authentication utilities
    └── jwt-utils.ts                    # JWT utilities (Redis-free)
```

### Admin Application
```
admin/src/
├── app/
│   ├── profile/page.tsx                # Admin profile with permissions
│   ├── settings/page.tsx               # Admin settings
│   └── api/auth/
│       └── login/route.ts              # Admin login API
├── context/
│   └── AdminAuthContext.tsx           # Admin authentication context
├── data/
│   └── mockUsers.ts                   # Admin mock data (copied)
└── lib/
    └── jwt-utils.ts                   # JWT utilities (Redis-free)
```

## 🔐 Authentication Implementation

### 1. Login Methods

#### Email/Password Login
```typescript
// Enhanced login API supports both email and phone
const emailLoginSchema = zod.object({
    email: zod.string().email("Invalid email address"),
    password: zod.string().min(6, "Password must be at least 6 characters"),
});

// Mock authentication check
const mockCredentials = mockLoginCredentials[identifier];
if (mockCredentials && mockCredentials.password === password) {
    user = mockCredentials.user;
    console.log('✅ Mock user authenticated:', user.name);
}
```

#### Phone/Password Login
```typescript
const phoneLoginSchema = zod.object({
    phone: zod.string().min(10, "Invalid phone number"),
    password: zod.string().min(6, "Password must be at least 6 characters"),
});

// Same mock authentication logic for phone numbers
```

#### OTP Login Flow
```typescript
// 1. Send OTP
export const sendOTP = async (phone: string) => {
    const otp = generateOTP(); // 6-digit random number
    const session = {
        phone: cleanPhone,
        otp,
        expiresAt: new Date(Date.now() + 5 * 60 * 1000), // 5 minutes
        attempts: 0,
        isVerified: false
    };
    otpSessions.set(cleanPhone, session);
};

// 2. Verify OTP
export const verifyOTP = async (phone: string, otp: string) => {
    const session = otpSessions.get(cleanPhone);
    if (session && session.otp === otp && !isOTPExpired(session)) {
        session.isVerified = true;
        return { success: true };
    }
    return { success: false };
};
```

### 2. JWT Token Management

#### Token Generation (Redis-Free)
```typescript
export function generateToken(payload: Omit<JWTPayload, 'jti'>, expiresIn: string = '1h'): string {
    const jti = uuidv4(); // Unique token identifier
    return jwt.sign(
        { ...payload, jti},
        process.env.JWT_SECRET,
        { expiresIn }
    );
}

// Simplified verification without Redis blacklist
export async function verifyToken(token: string): Promise<JWTPayload | null> {
    try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET) as JWTPayload;
        // Skip blacklist checking for development
        return decoded;
    } catch (error) {
        return null;
    }
}
```

### 3. Session Management

#### Cookie-Based Sessions
```typescript
// Set secure HTTP-only cookies
response.cookies.set("access_token", accessToken, {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: 'lax',
    path: '/',
    maxAge: 60 * 60 // 1 hour
});

response.cookies.set("refresh_token", refreshToken, {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: 'lax',
    path: '/',
    maxAge: 7 * 24 * 60 * 60 // 7 days
});
```

## 📊 Mock Data Implementation

### 1. User Data Structure

#### Customer Users
```typescript
export interface User {
  id: string;                    // 'customer_1', 'customer_2'
  name: string;                  // Full name
  email: string;                 // Login email
  phone: string;                 // Login phone
  address: string;               // Full address
  bio: string;                   // User description
  role: 'customer' | 'admin';    // User role
  
  // Customer-specific fields
  agentName?: string;            // Assigned agent
  agentImage?: string;           // Agent photo
  overdueAmount?: string;        // Outstanding overdue
  upcomingAmount?: string;       // Upcoming payments
  remainingAmount?: string;      // Total remaining
}
```

#### Admin Users
```typescript
export interface User {
  // ... base fields
  role: 'admin';
  
  // Admin-specific fields
  accessLevel: 'super_admin' | 'limited_admin';
  permissions: string[];         // Array of permission strings
}
```

### 2. Transaction Generation

#### Realistic Transaction Data
```typescript
const generateMonthlyTransactions = (userId: string): Transaction[] => {
  const transactionTypes = [
    { name: 'Property Maintenance Fee', category: 'Maintenance', type: 'debit' },
    { name: 'Monthly Rent Payment', category: 'Rent', type: 'debit' },
    { name: 'Security Deposit Refund', category: 'Refund', type: 'credit' },
    // ... more realistic property-related transactions
  ];

  // Generate 10 transactions per month for 12 months = 120 transactions
  for (let month = 0; month < 12; month++) {
    for (let day = 1; day <= 10; day++) {
      // Create realistic transaction with proper dates and amounts
    }
  }
};
```

### 3. Payment History

#### 24 Months of Payment Data
```typescript
const generatePaymentHistory = (userId: string): Payment[] => {
  const payments: Payment[] = [];
  
  for (let i = 0; i < 24; i++) { // 2 years of history
    const date = new Date();
    date.setMonth(date.getMonth() - i);
    
    payments.push({
      id: `${userId}_${15000 + i}`,
      date: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' }),
      amount: Math.floor(Math.random() * 2000) + 1000,
      totalQuestions: Math.floor(Math.random() * 5) + 1,
      status: ['Success', 'Pending', 'Rejected'][Math.floor(Math.random() * 3)],
      description: `Monthly payment for ${date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}`
    });
  }
  
  return payments;
};
```

## 🎨 UI/UX Implementation

### 1. Enhanced Login Page

#### OTP Integration
```typescript
// Add OTP option below phone input
<div className="mt-3">
  <button
    type="button"
    onClick={() => router.push(`/otp-login?phone=${encodeURIComponent(formData.phone)}`)}
    className="text-blue-800 hover:text-blue-700 font-medium transition-colors text-lg underline"
    disabled={!formData.phone}
  >
    Get OTP to login instead
  </button>
</div>
```

### 2. OTP Verification Page

#### 6-Digit OTP Input
```typescript
{otp.map((digit, index) => (
  <input
    key={index}
    id={`otp-${index}`}
    type="text"
    inputMode="numeric"
    pattern="[0-9]*"
    maxLength={1}
    value={digit}
    onChange={(e) => handleOtpChange(index, e.target.value)}
    onKeyDown={(e) => handleKeyDown(index, e)}
    className="w-12 h-12 text-center text-xl font-semibold border border-gray-300 rounded-lg focus:border-blue-800 focus:ring-1 focus:ring-blue-800 transition-all"
    required
  />
))}
```

### 3. Dashboard Integration

#### Dynamic Data Loading
```typescript
useEffect(() => {
  if (user) {
    const userData = getUserById(user.id);
    if (userData) {
      setUserData(userData);
      setUserPayments(getCustomerPayments(user.id));
      setUserTransactions(getCustomerTransactions(user.id));
    }
  }
}, [user]);
```

## 🔒 Security Features

### 1. Rate Limiting

#### In-Memory Failed Attempts
```typescript
const failedAttempts = new Map<string, { count: number; lastAttempt: Date }>();

export function hasTooManyAttempts(identifier: string): boolean {
  const attempts = failedAttempts.get(identifier);
  if (!attempts) return false;
  
  const now = new Date();
  const timeSinceLastAttempt = now.getTime() - attempts.lastAttempt.getTime();
  
  // Reset attempts if lockout period has passed
  if (timeSinceLastAttempt > LOCKOUT_DURATION) {
    failedAttempts.delete(identifier);
    return false;
  }
  
  return attempts.count >= MAX_ATTEMPTS;
}
```

### 2. Input Validation

#### Zod Schema Validation
```typescript
const emailLoginSchema = zod.object({
    email: zod.string().email("Invalid email address"),
    password: zod.string().min(6, "Password must be at least 6 characters"),
});

const phoneLoginSchema = zod.object({
    phone: zod.string().min(10, "Invalid phone number"),
    password: zod.string().min(6, "Password must be at least 6 characters"),
});
```

### 3. XSS Prevention

#### Input Sanitization
```typescript
export function sanitizeInput(input: string): string {
  return input
    .replace(/[<>]/g, '') // Remove angle brackets
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
}
```

## 🚀 Performance Optimizations

### 1. In-Memory Data Storage
- **Fast Access**: No database queries
- **Instant Load**: Immediate data availability
- **Development Friendly**: No setup required

### 2. Efficient State Management
- **Context API**: Centralized authentication state
- **Local Storage**: Persistent session data
- **Memory Caching**: Cached user data

### 3. Responsive Design
- **Mobile First**: Optimized for mobile devices
- **Progressive Enhancement**: Desktop features added progressively
- **Touch Friendly**: Proper touch targets and interactions

## 🔧 Development vs Production

### Development Features
- **Mock Data**: Complete offline functionality
- **OTP Display**: Console and toast notifications
- **Hot Reload**: Instant development feedback
- **No Dependencies**: No external services required

### Production Ready Features
- **JWT Security**: Production-grade token management
- **Rate Limiting**: Brute force protection
- **Input Validation**: Comprehensive security checks
- **Session Management**: Secure cookie handling

### Migration Path
1. **Replace Mock Data**: Connect to real database
2. **Add SMS Service**: Implement real OTP delivery
3. **Add Redis**: For session and blacklist management
4. **Environment Config**: Production environment variables

## 📈 Scalability Considerations

### Current Implementation
- **In-Memory Storage**: Suitable for development/testing
- **Single Instance**: No distributed concerns
- **Mock Services**: Simplified for rapid development

### Production Scaling
- **Database Integration**: PostgreSQL/MongoDB support ready
- **Redis Clustering**: Distributed session management
- **Load Balancing**: Stateless design supports scaling
- **Microservices**: Modular architecture allows service separation

---

## 📞 Support & Maintenance

### Code Quality
- **TypeScript**: Full type safety
- **Error Handling**: Comprehensive error management
- **Logging**: Detailed development logging
- **Testing Ready**: Structured for unit/integration tests

### Documentation
- **Inline Comments**: Detailed code documentation
- **API Documentation**: Clear endpoint descriptions
- **User Guides**: Comprehensive testing instructions
- **Architecture Docs**: System design documentation
