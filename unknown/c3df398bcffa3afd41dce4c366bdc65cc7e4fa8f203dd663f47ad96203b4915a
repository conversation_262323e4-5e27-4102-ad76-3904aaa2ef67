<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Message System Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>Message System Test</h1>
    
    <div class="container">
        <h2>Test Contact Form Submission</h2>
        <form id="contactForm">
            <div class="form-group">
                <label for="name">Name:</label>
                <input type="text" id="name" name="name" required>
            </div>
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" name="email" required>
            </div>
            <div class="form-group">
                <label for="phone">Phone:</label>
                <input type="tel" id="phone" name="phone">
            </div>
            <div class="form-group">
                <label for="subject">Subject:</label>
                <input type="text" id="subject" name="subject">
            </div>
            <div class="form-group">
                <label for="message">Message:</label>
                <textarea id="message" name="message" rows="4" required></textarea>
            </div>
            <div class="form-group">
                <label for="source">Source:</label>
                <select id="source" name="source">
                    <option value="contact_page">Contact Page</option>
                    <option value="about_page">About Page</option>
                    <option value="contact_form">Contact Form</option>
                </select>
            </div>
            <button type="submit">Submit Message</button>
        </form>
        <div id="submitResult"></div>
    </div>

    <div class="container">
        <h2>Test Database Setup (Main App)</h2>
        <button onclick="setupMainDatabase()">Setup Main App Database</button>
        <div id="setupMainResult"></div>
    </div>

    <div class="container">
        <h2>Test Database Setup (Admin App)</h2>
        <button onclick="setupAdminDatabase()">Setup Admin App Database</button>
        <div id="setupAdminResult"></div>
    </div>

    <div class="container">
        <h2>Test Message Statistics</h2>
        <button onclick="getStats()">Get Message Stats</button>
        <div id="statsResult"></div>
    </div>

    <script>
        // Test form submission
        document.getElementById('contactForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const data = {
                senderName: formData.get('name'),
                senderEmail: formData.get('email'),
                senderPhone: formData.get('phone'),
                subject: formData.get('subject'),
                messageContent: formData.get('message'),
                source: formData.get('source'),
                sourcePage: '/test'
            };

            try {
                const response = await fetch('http://localhost:3000/api/messages/submit', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                const resultDiv = document.getElementById('submitResult');
                
                if (result.success) {
                    resultDiv.innerHTML = `<div class="result success">Message submitted successfully! ID: ${result.messageId}</div>`;
                    document.getElementById('contactForm').reset();
                } else {
                    resultDiv.innerHTML = `<div class="result error">Error: ${result.error}</div>`;
                }
            } catch (error) {
                document.getElementById('submitResult').innerHTML = `<div class="result error">Network error: ${error.message}</div>`;
            }
        });

        // Test main app database setup
        async function setupMainDatabase() {
            try {
                const response = await fetch('http://localhost:3000/api/setup-db');
                const result = await response.json();
                const resultDiv = document.getElementById('setupMainResult');

                if (result.success) {
                    resultDiv.innerHTML = `<div class="result success">Main app database setup successful! Messages: ${result.messageCount}</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="result error">Setup failed: ${result.message}</div>`;
                }
            } catch (error) {
                document.getElementById('setupMainResult').innerHTML = `<div class="result error">Network error: ${error.message}</div>`;
            }
        }

        // Test admin app database setup
        async function setupAdminDatabase() {
            try {
                const response = await fetch('http://localhost:3001/api/setup-db');
                const result = await response.json();
                const resultDiv = document.getElementById('setupAdminResult');

                if (result.success) {
                    resultDiv.innerHTML = `<div class="result success">Admin app database setup successful!</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="result error">Setup failed: ${result.message}</div>`;
                }
            } catch (error) {
                document.getElementById('setupAdminResult').innerHTML = `<div class="result error">Network error: ${error.message}</div>`;
            }
        }

        // Test message statistics
        async function getStats() {
            try {
                const response = await fetch('http://localhost:3001/api/messages/stats');
                const result = await response.json();
                const resultDiv = document.getElementById('statsResult');
                
                if (result.success) {
                    resultDiv.innerHTML = `<div class="result success">
                        <h4>Message Statistics:</h4>
                        <p>Total: ${result.stats.total}</p>
                        <p>Unread: ${result.stats.unread}</p>
                        <p>Read: ${result.stats.read}</p>
                        <p>Replied: ${result.stats.replied}</p>
                    </div>`;
                } else {
                    resultDiv.innerHTML = `<div class="result error">Failed to get stats: ${result.error}</div>`;
                }
            } catch (error) {
                document.getElementById('statsResult').innerHTML = `<div class="result error">Network error: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
